<template>
  <div class="zq-home">
    <SearchBox />
    <swiper />
    <grid />
    <waterfall-list :goodsList="goodsList" :key="waterfallKey" @loadMore="loadMore" />
    <ProvinceServiceSelector v-model="showProvinceDrawer" @confirm="handleSelectionConfirm" />
  </div>
</template>

<script>
import swiper from './components/Swiper.vue'
import grid from './components/Grid.vue'
import WaterfallList from './components/WaterFallList'
import { curClassificationId, getBizCode } from '@/utils/curEnv'
import { getPartionList } from '@/api/goods'
import { getEnterpriseManagerInfo, queryZqInfo } from '@/utils/zqInfo'
import { zqQuerySimplified } from '@/api/zq'
import SearchBox from '@/components/ZQSearchs/index.vue'
import ProvinceServiceSelector from '@/components/ZQSelectFilter/Index.vue'
export default {
  components: {
    SearchBox,
    swiper,
    grid,
    WaterfallList,
    ProvinceServiceSelector
  },
  data() {
    return {
      typeList: [], // 首页分类
      classificationId: curClassificationId.get() || '',
      goodsList: [],
      pageNo: 1,
      pageSize: 10,
      allLoaded: false,
      goodsPoolIdSelected: '',
      isCustomerServiceShow: false,
      waterfallKey: Math.random(),
      showProvinceDrawer: false
    }
  },
  created() {
    // 获取商品分类信息
    this.initPage()
  },
  computed: {
    roleType() {
      const { roleType } = getEnterpriseManagerInfo()
      return roleType
    }
  },
  methods: {
    handleSelectionConfirm(selection) {
      console.log('确认选择，当前选中的省份ID:', selection.areaId)
      console.log('确认选择，当前选中的服务商ID:', selection.isvId)
      console.log('确认选择，当前选中的省份名称:', selection.provinceName)
      console.log('确认选择，当前选中的服务商名称:', selection.serviceName)

      // 重置页码并重新加载商品列表
      this.pageNo = 1
      this.goodsList = []

      // 根据当前选中的商品池ID重新加载商品
      // 这里会按照roleType === '4'的逻辑从provinceService store获取参数
      this.loadGoods('', '', 'recommend', '', true)

      // 关闭选择器
      this.showProvinceDrawer = false
    },
    async initPage() {
      this.$toast.queueLoading()
      const [err, json] = await getPartionList({
        bizCode: getBizCode('GOODS'),
        type: 2
      })
      this.$toast.queueClear()
      if (err) {
        this.$toast(err.msg)
        return
      }
      this.typeList = json ? json.sort((a, b) => b.pos - a.pos) : []
      // 获取为你推荐分类商品池数据
      const resultData = json
      if (resultData && resultData.length > 0) {
        const recommond = resultData[0]
        this.goodsPoolIdSelected = recommond.id
        this.changeGoodsPool(recommond.id, '')
      }
    },
    // 瀑布流商品加载
    async loadGoods(id, sortType, poolType = 'recommend', sort, isTabSwitch = false) {
      const zqInfo = queryZqInfo()

      // 根据roleType决定参数来源
      let finalId = id
      let supplierCode = this.roleType !== '4' ? (zqInfo.isvList[0].isvId ? zqInfo.isvList[0].isvId : '') : ''
      let proStr = this.roleType !== '4' ? zqInfo.provinceCode.join(',') : ''
      let type = 2
      if (this.roleType === '4') {
        // 当roleType为'4'时，从provinceService store获取数据
        finalId = ''
        const provinceServiceState = this.$store.state.provinceService
        supplierCode = provinceServiceState.selectedIsvId || ''
        proStr = provinceServiceState.selectedAreaId || ''
        type = 3
      }

      const data = {
        roleType: this.roleType,
        type,
        bizCode: getBizCode('GOODS'),
        id: finalId,
        supplierCode: supplierCode,
        proStr: proStr,
        pageNo: this.pageNo,
        pageSize: isTabSwitch ? this.pageSize : (this.pageNo === 1 && poolType === 'recommend' ? this.pageSize : this.pageSize),
        sort
      }
      if (sortType) {
        data.price_sort = sortType
      }
      this.$toast.queueLoading()
      const [err, json] = await zqQuerySimplified(data)
      this.$toast.queueClear()
      if (err) {
        this.$toast(err.msg)
        return
      }
      const listStore = []
      if (json && json.goodsList.length > 0) {
        json.goodsList.forEach(item => {
          const currSku = item && item.skuList ? item.skuList[0] : {}
          const comment = currSku.comment || ''
          // 活动价
          const promotionPrice = (currSku && currSku.skuPromotionList && currSku.skuPromotionList.length > 0)
            ? currSku.skuPromotionList[0].promotionPrice
            : ''
          const paramStr = []
          if (currSku.param) paramStr.push(currSku.param)
          if (currSku.param1) paramStr.push(currSku.param1)
          if (currSku.param2) paramStr.push(currSku.param2)
          if (currSku.param3) paramStr.push(currSku.param3)
          if (currSku.param4) paramStr.push(currSku.param4)

          const crossedPrice = currSku.crossedPrice ? (currSku.crossedPrice / 100).toFixed(2) : ''
          listStore.push({
            paramStr: paramStr.join(' '),
            sortNo: item && item.sortNo ? item.sortNo : '',
            goodsImg: item.listImageUrl ? item.listImageUrl.split(',')[0] : '',
            goodsName: currSku ? currSku.name : '',
            goodsPrice: currSku ? `¥${(currSku.lowPrice / 100).toFixed(2)} - ¥${(currSku.highPrice / 100).toFixed(2)}` : '',
            lowPrice: currSku ? (currSku.lowPrice / 100).toFixed(2) : '',
            highPrice: currSku ? (currSku.highPrice / 100).toFixed(2) : '',
            goodsId: item && item.id ? item.id : '',
            goodsSkuId: currSku ? currSku.skuId : '',
            comment,
            goodsPromotionPrice: promotionPrice ? (promotionPrice / 100).toFixed(2) : '',
            goodsTips: item && item.tags ? item.tags.split(',') : [],
            goodsJb: (item && item.markImageUrl) ? item.markImageUrl : '',
            goodsCrossedPrice: crossedPrice// 划线价
          })
        })
      }

      if (this.pageNo === 1) {
        this.goodsList = listStore
      } else {
        this.goodsList = this.goodsList.concat(listStore)
      }
      if (json.cacheType === '1') {
        this.pageNo++
        this.allLoaded = true
      } else {
        this.allLoaded = false
      }
    },
    loadMore() {
      if (this.allLoaded) {
        this.loadGoods(this.goodsPoolIdSelected)
      }
    },
    switchTabs(id) {
      this.pageNo = 1
      this.goodsPoolIdSelected = id
      this.loadGoods(id, '', 'recommend', '', true)
    },
    changeGoodsPool(id, sortType) {
      this.classificationId = id
      // 打一个标志，在查询
      // this.resetSortType = true
      curClassificationId.set(id)
      this.loadGoods(id, sortType)
    }
  },
  activated() {
    this.waterfallKey = Math.random() // 手动触发重新渲染
  },
  deactivated() {
    this.$toast.clear()
  }
}
</script>

<style lang='less' scoped>
@screen-small: 320px;
@screen-medium: 540px;



.province-drawer {
  display: flex;
  flex-direction: column;
  height: 100%;

  .drawer-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 10px;
    // border-bottom: 1px solid #f0f0f0;

    .drawer-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .drawer-close {
      font-size: 24px;
      color: #999;
      cursor: pointer;
    }
  }

  .drawer-content {
    padding: 10px;
    flex: 1;
    overflow: auto;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
    }

    .province-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
      margin-bottom: 30px;

      .province-item {
        padding: 5px;
        text-align: center;
        background: #f8f9fa;
        border-radius: 4px;
        cursor: pointer;
        box-sizing: border-box;
        overflow: hidden;
        text-overflow: ellipsis; //文本溢出显示省略号
        white-space: nowrap; //文本不会换行

        &.active {
          background: #ff6b35;
          color: white;
        }

        .province-name {
          font-size: 13px;
          overflow: hidden;
          text-overflow: ellipsis; //文本溢出显示省略号
          white-space: nowrap; //文本不会换行
        }
      }
    }

    .service-section {
      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 16px;
      }

      .service-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        margin-bottom: 30px;

        .service-item {
          padding: 6px;
          text-align: center;
          background: #f8f9fa;
          border-radius: 4px;
          cursor: pointer;

          overflow: hidden;
          text-overflow: ellipsis; //文本溢出显示省略号
          white-space: nowrap; //文本不会换行

          &.active {
            background: #ff6b35;
            color: white;
          }

          .service-name {
            font-size: 13px;
            overflow: hidden;
            text-overflow: ellipsis; //文本溢出显示省略号
            white-space: nowrap; //文本不会换行
          }
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 12px;
    padding: 10px;
    box-sizing: border-box;

    .reset-btn,
    .confirm-btn {
      flex: 1;
      padding: 8px;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      box-sizing: border-box;
    }

    .reset-btn {
      background: #f8f9fa;
      color: #666;
    }

    .confirm-btn {
      background: #ff6b35;
      color: white;
    }
  }
}

//安卓8以下单独处理
.android_8 {
  .zq-home {
    padding-bottom: 49px;
  }
}

::-webkit-scrollbar {
  /*隐藏滚轮*/
  display: none;
}

.zq-home {
  background: #F8F9FA;
  //padding-top: 10px;
  padding-bottom: calc(49px + var(--saib));
  min-height: 100vh;
  overflow-y: scroll;
}

.tabbar {
  position: fixed;
  bottom: 0;
}
</style>
