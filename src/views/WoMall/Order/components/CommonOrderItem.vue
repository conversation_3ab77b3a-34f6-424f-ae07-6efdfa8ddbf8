<template>
  <article class="common-order-item">
    <WoCard>
      <header class="common-order-item__header">
        <div class="common-order-item__number-wrapper">
          <span class="common-order-item__number-text">订单号：{{ order.id }}</span>
          <img src="@/static/images/copy.png" alt="复制" class="common-order-item__copy-icon"
            @click.stop="handleCopyOrder" />
        </div>

        <OrderCountdown v-if="order.orderState === '0' && order.remainingTime > 0"
          :remaining-time="order.remainingTime" />

        <div v-else class="common-order-item__status" :class="getStatusClass(order.orderState)">
          {{ orderState(order.orderState) }}
        </div>
      </header>

      <section class="common-order-item__goods">
        <OrderGoodsCard :key="order.id" :item="order" :image-size="75" :min-height="110" :showActions="true"
          :moreActions="computedMoreActions" :itemId="order.id" @click="handleDetailClick">
          <template #actions>
            <div ref="buttonsContainerRef" class="buttons-container">
              <WoButton v-for="button in computedVisibleButtons" :key="button.text" :type="getButtonType(button.color)"
                size="small" @click="button.handler">
                {{ button.text }}
              </WoButton>
            </div>
          </template>
        </OrderGoodsCard>
      </section>
    </WoCard>
  </article>
</template>

<script setup>
import { toRefs, ref, computed, onMounted, onUnmounted } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import OrderGoodsCard from '@components/GoodsCommon/OrderGoodsCard.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import OrderCountdown from './OrderCountdown.vue'
import orderState from '@utils/orderState.js'
import { useOrderButtons } from '../composables/useOrderButtons.js'

const props = defineProps({
  order: {
    type: Object,
    required: true
  },
  visibleButtons: {
    type: Array,
    default: () => []
  },
  moreActions: {
    type: Array,
    default: () => []
  }
})

const { order, visibleButtons: originalVisibleButtons, moreActions: originalMoreActions } = toRefs(props)

const emit = defineEmits(['detail-click', 'copy-order'])

const { getButtonType, getStatusClass } = useOrderButtons()

// 响应式按钮管理
const buttonsContainerRef = ref(null)
const screenWidth = ref(window.innerWidth)

// 监听屏幕宽度变化
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', updateScreenWidth)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenWidth)
})

// 计算最终的按钮分配
const computedVisibleButtons = computed(() => {
  // 如果屏幕宽度大于某个阈值，使用原始分配
  if (screenWidth.value > 480) {
    return originalVisibleButtons.value
  }

  // 小屏幕时，合并所有按钮重新分配
  const allButtons = [...originalVisibleButtons.value, ...originalMoreActions.value]

  // 简单策略：小屏幕时最多显示2个按钮
  return allButtons.slice(0, 2)
})

const computedMoreActions = computed(() => {
  // 如果屏幕宽度大于某个阈值，使用原始分配
  if (screenWidth.value > 480) {
    return originalMoreActions.value
  }

  // 小屏幕时，剩余的按钮都放到更多操作中
  const allButtons = [...originalVisibleButtons.value, ...originalMoreActions.value]

  // 创建深拷贝避免修改原始数组
  const copiedButtons = allButtons.map(item => ({
    ...item,
    color: 'gray'
  }))

  return copiedButtons.slice(2)
})

const handleDetailClick = () => {
  emit('detail-click', order.value)
}

const handleCopyOrder = () => {
  emit('copy-order', order.value.id)
}
</script>

<style scoped lang="less">
.common-order-item {
  margin-bottom: @padding-page * 2;

  &:last-child {
    margin-bottom: 0;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;
  }

  &__number-wrapper {
    display: flex;
    align-items: center;
    margin-right: 15px;
    width: 100%;
    overflow: hidden;
  }

  &__number-text {
    font-size: @font-size-11;
    color: @text-color-secondary;
    margin-right: 3px;
    .ellipsis();
  }

  &__copy-icon {
    width: 10px;
    height: 10px;
    cursor: pointer;
  }

  &__status {
    flex-shrink: 0;
    font-size: @font-size-14;
    font-weight: @font-weight-600;

    &.order-status--unpaid {
      color: @theme-color;
    }

    &.order-status--completed {
      color: @text-color-secondary;
    }
  }

  &__goods {
    margin-bottom: @padding-page * 2;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.buttons-container {
  display: flex;
  gap: 8px;
  width: 100%;
  overflow: hidden;
}
</style>
