<template>
  <article class="address-skeleton">
    <header class="address-skeleton__header">
      <div class="contact-info-skeleton">
        <div class="skeleton-line skeleton-name"></div>
        <div class="skeleton-line skeleton-phone"></div>
      </div>
    </header>

    <div class="address-skeleton__content">
      <div class="skeleton-line skeleton-address"></div>
      <div class="skeleton-line skeleton-address-detail"></div>
    </div>

    <footer class="address-skeleton__actions">
      <div class="skeleton-line skeleton-btn"></div>
      <div class="skeleton-line skeleton-btn"></div>
    </footer>
  </article>
</template>

<script setup>
// 骨架屏组件不需要任何逻辑
</script>

<style scoped lang="less">
.address-skeleton {
  padding: 15px;
  background-color: @bg-color-white;
  border-radius: @radius-4;
  margin-bottom: 8px;
  contain: layout style paint;

  &__header {
    margin-bottom: 11px;
  }

  &__content {
    margin-bottom: 16px;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}

.contact-info-skeleton {
  display: flex;
  align-items: center;
  gap: 10px;
}

.skeleton-line {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
  will-change: background-position;
}

.skeleton-name {
  width: 60px;
  height: 16px;
}

.skeleton-phone {
  width: 100px;
  height: 16px;
}

.skeleton-address {
  width: 100%;
  height: 13px;
  margin-bottom: 6px;
}

.skeleton-address-detail {
  width: 80%;
  height: 13px;
}

.skeleton-btn {
  width: 40px;
  height: 25px;
  border-radius: @radius-4;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>
