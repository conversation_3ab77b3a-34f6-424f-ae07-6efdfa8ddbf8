<template>
  <van-popup v-model:show="visible" round position="bottom" :style="{ height: '80%' }" @close="handleClose">
    <div class="address-quick-selection-popup">
      <div class="popup-header">
        <h3 class="title">选择地址</h3>
        <div class="close-btn" @click="handleClose">
          <img src="../../../static/images/close.png" alt="关闭" loading="lazy" />
        </div>
      </div>

      <div class="address-tips" v-if="showTips && tipsText">
        <span class="tips-text" v-html="tipsText"></span>
      </div>

      <!-- 骨架屏 -->
      <div class="address-list" v-if="loading">
        <div v-for="index in 2" :key="`skeleton-${index}`">
          <AddressItemSkeleton />
        </div>
      </div>

      <!-- 地址列表 -->
      <div class="address-list" ref="addressListRef" v-else-if="hasAddressList">
        <div v-for="address in actualAddressList" :key="address.addressId"
          :ref="el => setAddressItemRef(el, address.addressId)" :data-address-id="address.addressId">
          <AddressItem :address="address" @click="handleSelectAddress" @edit="handleEditAddress"
            @delete="handleDeleteAddress" />
        </div>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-else>
        <img src="./assets/no-address.png" alt="暂无地址" class="empty-image" loading="lazy" decoding="async" />
      </div>

      <WoActionBar class="action-bar">
        <WoButton type="primary" block size="xlarge" @click="handleCreateNewAddress">
          新建收货地址
        </WoButton>
      </WoActionBar>
    </div>
  </van-popup>
</template>

<script setup>
import { defineProps, defineEmits, toRefs, ref, watch, nextTick, computed, shallowRef, onUnmounted, readonly, markRaw, defineAsyncComponent } from 'vue'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { useUserStore } from '@store/modules/user.js'
import { updateUserDefaultAddr } from '@api/index.js'
import { debounce } from 'lodash-es'

const WoButton = defineAsyncComponent(() => import('@components/WoElementCom/WoButton/WoButton.vue'))
const WoActionBar = defineAsyncComponent(() => import('@components/WoElementCom/WoActionBar.vue'))
const AddressItem = defineAsyncComponent(() => import('@components/Common/Address/AddressItem.vue'))
const AddressItemSkeleton = defineAsyncComponent(() => import('@components/Common/Address/AddressItemSkeleton.vue'))

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  addressList: {
    type: Array,
    default: null
  },
  selectedAddressId: {
    type: [String, Number],
    default: null
  },
  showTips: {
    type: Boolean,
    default: false
  },
  tipsText: {
    type: String,
    default: '23:10前付款，预计明天（03月04日）到达'
  }
})

const emit = defineEmits([
  'close',
  'select',
  'create',
  'edit',
  'delete',
  'update:visible'
])

const { visible, addressList, selectedAddressId, showTips, tipsText } = toRefs(props)
const userStore = useUserStore()

const internalAddressList = shallowRef([])
const internalSelectedAddressId = ref(null)
const loading = ref(false)

const addressListRef = ref(null)
const addressItemRefs = shallowRef(new Map())

const actualAddressList = computed(() => {
  return addressList.value !== null ? addressList.value : internalAddressList.value
})

const hasAddressList = computed(() => {
  return actualAddressList.value.length > 0
})

const loadAddressListFromStore = async () => {
  if (loading.value) return

  loading.value = true
  try {
    await userStore.queryLoginStatus()

    if (userStore.isLogin) {
      await Promise.all([
        userStore.queryDefaultAddr({force: true}),
        userStore.queryAddrList({force: true})
      ])
    }

    internalAddressList.value = markRaw(userStore.addressList || [])

    const defaultAddress = internalAddressList.value.find(item => item.isDefault === '1')
    if (defaultAddress) {
      internalSelectedAddressId.value = defaultAddress.addressId
    }
  } catch (error) {
    console.error('加载地址列表失败:', error)
    internalAddressList.value = []
  } finally {
    loading.value = false
  }
}

const setAddressItemRef = (el, addressId) => {
  if (el) {
    addressItemRefs.value.set(addressId, el)
  } else {
    addressItemRefs.value.delete(addressId)
  }
}

const scrollToAddress = debounce((addressId) => {
  if (addressListRef.value && addressId) {
    const selectedElement = addressItemRefs.value.get(addressId);
    if (selectedElement) {
      selectedElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  }
}, 100);

const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

const handleSelectAddress = debounce(async (address) => {
  if (loading.value) return

  showLoadingToast()

  try {
    const [err] = await updateUserDefaultAddr(address.addressId)

    if (!err) {
      const currentList = actualAddressList.value
      currentList.forEach(item => {
        item.isDefault = item.addressId === address.addressId ? '1' : '0'
      })

      if (addressList.value === null) {
        internalAddressList.value = markRaw([...currentList])
      }

      userStore.setAddrList(currentList)
      userStore.setDefaultAddr(address)

      showToast('设置默认地址成功')

      emit('select', address)
      handleClose()
    } else {
      console.error('设置默认地址失败:', err)
      showToast(err.msg || '设置默认地址失败')
    }
  } catch (error) {
    console.error('设置默认地址异常:', error)
    showToast('设置默认地址失败')
  } finally {
    closeToast()
  }
}, 300)

const handleCreateNewAddress = () => {
  emit('create')
}

const handleEditAddress = (address) => {
  emit('edit', address)
}

const handleDeleteAddress = (address) => {
  emit('delete', address)
}

watch(visible, async (newVisible) => {
  if (newVisible) {
    await loadAddressListFromStore()

    nextTick(() => {
      let targetAddressId = null

      if (selectedAddressId.value !== null) {
        targetAddressId = selectedAddressId.value
      } else {
        const currentList = actualAddressList.value
        const defaultAddress = currentList.find(item => item.isDefault === '1')
        if (defaultAddress) {
          targetAddressId = defaultAddress.addressId
          if (addressList.value === null) {
            internalSelectedAddressId.value = defaultAddress.addressId
          }
        }
      }

      if (targetAddressId) {
        scrollToAddress(targetAddressId)
      }
    })
  }
}, { immediate: false })

onUnmounted(() => {
  scrollToAddress.cancel();
});

defineExpose({
  scrollToAddress,
  loadAddressList: loadAddressListFromStore,
  addressList: readonly(internalAddressList)
})
</script>

<style scoped lang="less">
.address-quick-selection-popup {
  background: @bg-color-gray;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-bottom: 65px;
  box-sizing: border-box;
  contain: layout style paint;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 16px 21px;
    flex-shrink: 0;
    contain: layout style;

    .title {
      font-size: @font-size-17;
      font-weight: @font-weight-600;
      color: @text-color-primary;
      margin: 0;
      text-align: center;
    }

    .close-btn {
      position: absolute;
      right: 20px;
      width: 24px;
      height: 24px;
      border: none;
      background: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      will-change: opacity;

      img {
        width: 24px;
        height: 24px;
      }

      &:hover {
        opacity: @opacity-07;
      }
    }
  }

  .address-tips {
    margin: 0 10px 10px 10px;
    contain: layout style;

    .tips-text {
      color: @text-color-tips;
      font-size: @font-size-15;
      line-height: 1.5;
    }
  }

  .address-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 10px;
    will-change: scroll-position;
    contain: layout style paint;
    transform: translateZ(0);

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: @text-color-disabled;
      border-radius: @radius-2;
    }

    &::-webkit-scrollbar-thumb {
      background: @text-color-tertiary;
      border-radius: @radius-2;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: @text-color-secondary;
    }
  }

  .empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    contain: layout style;

    .empty-image {
      width: 200px;
      height: 200px;
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }

    .empty-text {
      font-size: @font-size-16;
      color: @text-color-secondary;
      margin: 0;
    }
  }
}
</style>
