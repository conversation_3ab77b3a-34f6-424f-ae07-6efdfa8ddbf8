<template>
  <van-popup v-model:show="visible" position="right" :style="{ height: '100%', width: '75%' }" safe-area-inset-bottom>
    <div class="province-drawer">
      <div class="drawer-header">
        <div class="drawer-close" @click="handleClose">×</div>
      </div>
      <div class="drawer-content">
        <div class="service-section">
          <div class="section-title">选择省分</div>
          <div class="province-grid">
            <!-- 全部选项 -->
            <div class="province-item" :class="{ active: selectedAreaId === '' }" @click="selectProvince('')">
              <div class="province-name">全部</div>
            </div>
            <!-- 省分选项 -->
            <div class="province-item" v-for="province in provinceList" :key="province.areaId"
              :class="{ active: selectedAreaId === province.areaId }" @click="selectProvince(province.areaId)">
              <div class="province-name">{{ province.areaName }}</div>
            </div>
          </div>
        </div>
        <div class="service-section">
          <div class="section-title">选择服务商</div>
          <div class="service-grid">
            <!-- 全部选项 -->
            <div class="service-item" :class="{ active: selectedIsvId === '' }" @click="selectService('')">
              <div class="service-name">全部</div>
            </div>
            <!-- 服务商选项 -->
            <div class="service-item" v-for="service in serviceList" :key="service.isvId"
              :class="{ active: selectedIsvId === service.code }" @click="selectService(service.code)">
              <div class="service-name">{{ service.name }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 确定和重置按钮 -->
      <div class="action-buttons">
        <div class="reset-btn" @click="resetSelection">重置</div>
        <div class="confirm-btn" @click="confirmSelection">确定</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useProvinceServiceStore } from '@/store/modules/provinceService.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'confirm'])

const provinceServiceStore = useProvinceServiceStore()

// Computed properties
const provinceList = computed(() => provinceServiceStore.provinceList)
const serviceList = computed(() => provinceServiceStore.serviceList)
const selectedProvinceName = computed(() => provinceServiceStore.selectedProvinceName)
const selectedServiceName = computed(() => provinceServiceStore.selectedServiceName)

const visible = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  }
})

const selectedAreaId = computed({
  get() {
    return provinceServiceStore.selectedAreaId
  },
  set(val) {
    provinceServiceStore.selectProvince(val)
  }
})

const selectedIsvId = computed({
  get() {
    return provinceServiceStore.selectedIsvId
  },
  set(val) {
    provinceServiceStore.selectService(val)
  }
})

// Methods
const selectProvince = (areaId) => {
  provinceServiceStore.selectProvince(areaId)
}

const selectService = (code) => {
  provinceServiceStore.selectService(code)
}

const handleClose = () => {
  visible.value = false
}

const resetSelection = () => {
  // 调用store中的resetSelection重置数据到全部
  provinceServiceStore.resetSelection()
  // 触发确认逻辑
  confirmSelection()
}

const confirmSelection = () => {
  visible.value = false
  emit('confirm', {
    areaId: selectedAreaId.value,
    isvId: selectedIsvId.value,
    provinceName: selectedProvinceName.value,
    serviceName: selectedServiceName.value
  })
}

const fetchServiceList = () => {
  return provinceServiceStore.fetchServiceList()
}

// Lifecycle
onMounted(async () => {
  // 如果serviceList为空，则自动获取服务商列表
  if (!serviceList.value || serviceList.value.length === 0) {
    await fetchServiceList()
  }
})
</script>

<style lang="less" scoped>
.province-drawer {
  display: flex;
  flex-direction: column;
  height: 100%;

  .drawer-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 10px;

    .drawer-close {
      font-size: 24px;
      color: #999;
      cursor: pointer;
    }
  }

  .drawer-content {
    padding: 10px;
    flex: 1;
    overflow: auto;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
    }

    .province-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
      margin-bottom: 30px;

      .province-item {
        padding: 5px;
        text-align: center;
        background: #f8f9fa;
        border-radius: 4px;
        cursor: pointer;
        box-sizing: border-box;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.active {
          background: #ff6b35;
          color: white;
        }

        .province-name {
          font-size: 13px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .service-section {
      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 16px;
      }

      .service-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        margin-bottom: 30px;

        .service-item {
          padding: 6px;
          text-align: center;
          background: #f8f9fa;
          border-radius: 4px;
          cursor: pointer;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          &.active {
            background: #ff6b35;
            color: white;
          }

          .service-name {
            font-size: 13px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 12px;
    padding: 10px;
    box-sizing: border-box;

    .reset-btn,
    .confirm-btn {
      flex: 1;
      padding: 8px;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      box-sizing: border-box;
    }

    .reset-btn {
      background: #f8f9fa;
      color: #666;
    }

    .confirm-btn {
      background: #ff6b35;
      color: white;
    }
  }
}
</style>